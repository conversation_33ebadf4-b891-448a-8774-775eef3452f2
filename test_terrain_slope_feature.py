#!/usr/bin/env python3
"""
测试脚本：验证地形坡度特征的实现
验证观测维度、奖励函数和配置的正确性

使用方法：
cd final_project
python ../test_terrain_slope_feature.py
"""

import sys
import os
import torch
import numpy as np

# 添加路径以导入legged_gym模块
current_dir = os.path.dirname(os.path.abspath(__file__))
final_project_dir = os.path.join(current_dir, 'final_project')
legged_gym_dir = os.path.join(final_project_dir, 'legged_gym')
sys.path.insert(0, legged_gym_dir)

from legged_gym.envs.g1.g1_config import G1RoughCfg
from legged_gym.envs.g1.g1 import G1Rough

def test_terrain_slope_implementation():
    """测试地形坡度特征的完整实现"""
    
    print("=" * 60)
    print("测试地形坡度特征实现")
    print("=" * 60)
    
    # 1. 测试配置
    print("\n1. 测试配置...")
    cfg = G1RoughCfg()
    
    # 检查观测维度
    print(f"   观测维度数量: {cfg.env.num_observations}")
    
    # 检查观测缩放参数
    if hasattr(cfg.normalization.obs_scales, 'slope'):
        print(f"   坡度观测缩放: {cfg.normalization.obs_scales.slope}")
    else:
        print("   ❌ 缺少坡度观测缩放参数")
        return False
    
    # 检查奖励缩放参数
    if hasattr(cfg.rewards.scales, 'terrain_slope_penalty'):
        print(f"   坡度惩罚权重: {cfg.rewards.scales.terrain_slope_penalty}")
    else:
        print("   ❌ 缺少坡度惩罚权重参数")
        return False
    
    # 2. 创建环境实例（小规模测试）
    print("\n2. 创建测试环境...")
    
    # 修改配置为小规模测试
    cfg.env.num_envs = 4  # 减少环境数量以加快测试
    cfg.terrain.mesh_type = "trimesh"  # 确保使用地形网格
    cfg.terrain.measure_heights = True  # 确保启用高度测量
    
    try:
        env = G1Rough(cfg=cfg, sim_params=None, physics_engine=None, sim_device="cpu", headless=True)
        print("   ✓ 环境创建成功")
    except Exception as e:
        print(f"   ❌ 环境创建失败: {e}")
        return False
    
    # 3. 测试观测维度
    print("\n3. 测试观测维度...")
    
    try:
        # 重置环境
        obs, _ = env.reset()
        
        # 检查观测维度
        expected_obs_dim = cfg.env.num_observations
        actual_obs_dim = obs.shape[1]
        
        print(f"   期望观测维度: {expected_obs_dim}")
        print(f"   实际观测维度: {actual_obs_dim}")
        
        if actual_obs_dim == expected_obs_dim:
            print("   ✓ 观测维度匹配")
        else:
            print("   ❌ 观测维度不匹配")
            return False
            
        # 检查观测值范围
        print(f"   观测值范围: [{obs.min().item():.3f}, {obs.max().item():.3f}]")
        
        # 检查是否有NaN或Inf
        if torch.isnan(obs).any():
            print("   ❌ 观测中包含NaN值")
            return False
        if torch.isinf(obs).any():
            print("   ❌ 观测中包含Inf值")
            return False
        
        print("   ✓ 观测值正常")
        
    except Exception as e:
        print(f"   ❌ 观测测试失败: {e}")
        return False
    
    # 4. 测试地形特征计算
    print("\n4. 测试地形特征计算...")
    
    try:
        # 检查是否有地形特征
        if hasattr(env, 'terrain_features') and "slope_mag" in env.terrain_features:
            slope_mag = env.terrain_features["slope_mag"]
            print(f"   坡度特征形状: {slope_mag.shape}")
            print(f"   坡度值范围: [{slope_mag.min().item():.3f}, {slope_mag.max().item():.3f}]")
            
            # 检查坡度值是否在合理范围内 [0, 1]
            if slope_mag.min() >= 0 and slope_mag.max() <= 1:
                print("   ✓ 坡度值在合理范围内")
            else:
                print("   ⚠️  坡度值超出预期范围 [0, 1]")
            
            print("   ✓ 地形特征计算正常")
        else:
            print("   ❌ 地形特征未计算或缺失")
            return False
            
    except Exception as e:
        print(f"   ❌ 地形特征测试失败: {e}")
        return False
    
    # 5. 测试奖励函数
    print("\n5. 测试奖励函数...")
    
    try:
        # 检查奖励函数是否存在
        if hasattr(env, '_reward_terrain_slope_penalty'):
            # 计算奖励
            reward = env._reward_terrain_slope_penalty()
            
            print(f"   坡度惩罚奖励形状: {reward.shape}")
            print(f"   奖励值范围: [{reward.min().item():.3f}, {reward.max().item():.3f}]")
            
            # 检查奖励值是否正常
            if torch.isnan(reward).any():
                print("   ❌ 奖励中包含NaN值")
                return False
            if torch.isinf(reward).any():
                print("   ❌ 奖励中包含Inf值")
                return False
            
            print("   ✓ 奖励函数计算正常")
        else:
            print("   ❌ 坡度惩罚奖励函数不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ 奖励函数测试失败: {e}")
        return False
    
    # 6. 测试环境步进
    print("\n6. 测试环境步进...")
    
    try:
        # 执行几个步骤
        for i in range(3):
            actions = torch.zeros(env.num_envs, env.num_actions, device=env.device)
            obs, rewards, dones, infos = env.step(actions)
            
            print(f"   步骤 {i+1}: 观测形状 {obs.shape}, 奖励范围 [{rewards.min().item():.3f}, {rewards.max().item():.3f}]")
            
            # 检查观测和奖励是否正常
            if torch.isnan(obs).any() or torch.isnan(rewards).any():
                print("   ❌ 步进过程中出现NaN值")
                return False
        
        print("   ✓ 环境步进正常")
        
    except Exception as e:
        print(f"   ❌ 环境步进测试失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✓ 所有测试通过！地形坡度特征实现正确。")
    print("=" * 60)
    
    return True

def print_implementation_summary():
    """打印实现总结"""
    print("\n" + "=" * 60)
    print("实现总结")
    print("=" * 60)
    
    print("\n已实现的功能：")
    print("1. 地形特征计算 (_compute_terrain_features)")
    print("   - 基于 measured_heights 计算局部坡度幅值")
    print("   - 使用中心差分方法计算梯度")
    print("   - 归一化到 [0, 1] 范围")
    print("   - 缓存到 self.terrain_features['slope_mag']")
    
    print("\n2. 观测空间扩展 (compute_observations)")
    print("   - 将坡度特征拼接到观测向量")
    print("   - 应用观测缩放 obs_scales.slope")
    print("   - 更新 num_observations: 268 → 269")
    
    print("\n3. 奖励函数集成 (_reward_terrain_slope_penalty)")
    print("   - 基于坡度幅值计算惩罚")
    print("   - 使用平方函数增强惩罚效果")
    print("   - 配置权重 terrain_slope_penalty = -0.05")
    
    print("\n4. 配置更新")
    print("   - obs_scales.slope = 1.0")
    print("   - rewards.scales.terrain_slope_penalty = -0.05")
    print("   - num_observations = 269")
    
    print("\n使用方法：")
    print("1. 训练时坡度特征会自动计算并加入观测")
    print("2. 奖励函数会根据地形坡度给予适当的惩罚")
    print("3. 可通过调整 terrain_slope_penalty 权重来控制惩罚强度")
    print("4. 可通过调整 obs_scales.slope 来控制观测的敏感度")

if __name__ == "__main__":
    print("开始测试地形坡度特征实现...")
    
    success = test_terrain_slope_implementation()
    
    if success:
        print_implementation_summary()
    else:
        print("\n❌ 测试失败，请检查实现。")
        sys.exit(1)
