#!/usr/bin/env python3
"""
检查 G1 观测维度是否正确匹配配置
"""

def calculate_expected_obs_dimensions():
    """计算预期的观测维度"""
    print("计算 G1 观测维度:")
    
    # 基础观测维度
    commands = 3  # x, y, yaw 速度指令
    base_lin_vel = 3  # x, y, z 线速度
    base_ang_vel = 3  # x, y, z 角速度
    projected_gravity = 3  # 重力投影
    dof_pos = 23  # G1 有 23 个自由度
    dof_vel = 23  # 关节速度
    actions = 23  # 动作
    
    base_obs = commands + base_lin_vel + base_ang_vel + projected_gravity + dof_pos + dof_vel + actions
    print(f"  基础观测: {base_obs}")
    print(f"    - commands: {commands}")
    print(f"    - base_lin_vel: {base_lin_vel}")
    print(f"    - base_ang_vel: {base_ang_vel}")
    print(f"    - projected_gravity: {projected_gravity}")
    print(f"    - dof_pos: {dof_pos}")
    print(f"    - dof_vel: {dof_vel}")
    print(f"    - actions: {actions}")
    
    # 地形高度测量
    # 根据 legged_robot_config.py: measured_points_x = 17, measured_points_y = 11
    height_measurements = 17 * 11  # 187
    print(f"  高度测量: {height_measurements} (17 x 11)")
    
    # 地形特征
    slope_features = 1  # 坡度幅值
    print(f"  地形特征: {slope_features}")
    
    total_expected = base_obs + height_measurements + slope_features
    print(f"  预期总维度: {total_expected}")
    
    return total_expected

def check_config():
    """检查配置文件中的设置"""
    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'legged_gym', 'legged_gym'))
        
        from envs.g1.g1_config import G1RoughCfg
        cfg = G1RoughCfg()
        
        print(f"\n配置文件中的观测维度: {cfg.env.num_observations}")
        
        # 检查观测缩放参数
        obs_scales = cfg.normalization.obs_scales
        print(f"观测缩放参数:")
        for attr in dir(obs_scales):
            if not attr.startswith('_'):
                print(f"  {attr}: {getattr(obs_scales, attr)}")
        
        return cfg.env.num_observations
        
    except Exception as e:
        print(f"无法加载配置: {e}")
        return None

def main():
    print("=" * 50)
    print("G1 观测维度检查")
    print("=" * 50)
    
    expected = calculate_expected_obs_dimensions()
    configured = check_config()
    
    print("\n" + "=" * 50)
    print("结果对比:")
    print(f"预期维度: {expected}")
    print(f"配置维度: {configured}")
    
    if configured is not None:
        if expected == configured:
            print("✓ 维度匹配正确!")
        else:
            print("❌ 维度不匹配!")
            print(f"差异: {configured - expected}")
            
            if configured > expected:
                print("配置维度过大，可能导致网络输入维度错误")
            else:
                print("配置维度过小，可能缺少某些观测特征")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
