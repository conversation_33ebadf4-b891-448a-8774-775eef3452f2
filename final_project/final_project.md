# 基于 IsaacGym 的人形机器人复杂地形全身仿人行走 #

### 安装流程 ###
1. 前期准备：Isaac Gym Preview 4 Release 目前仅支持 Linux 操作系统（为满足显存需求，推荐[配置腾讯云 + VNC](https://ffy6511.github.io/2025/06/28/%E6%9C%8D%E5%8A%A1%E5%99%A8/VNC%E4%B8%8E%E4%BA%91%E6%9C%8D%E5%8A%A1%E5%99%A8/#%E5%87%86%E5%A4%87%E4%BA%91%E6%9C%8D%E5%8A%A1%E5%99%A8)）
2. 使用 miniconda/anaconda 创建新的 python 虚拟环境，- `conda create -n locomotion python=3.8`
3. 安装 pytorch，- `pip3 install torch torchvision torchaudio`
4. 安装 Isaac Gym
    - 运行以下命令: -`cd isaacgym/python && pip install -e .`
    - 运行 Isaac Gym 提供的样例: -`cd examples && python 1080_balls_of_solitude.py`
    - 遇到问题可以查看本地网页文档 `isaacgym/docs/index.html`
5. 安装 rsl_rl (用于足式机器人强化学习训练的 PPO 实现)
    - 运行以下命令: -`cd rsl_rl && pip install -e .`
6. 安装 legged_gym
    - 运行以下命令: -`cd legged_gym && pip install -e .`


### 环境说明 ###
1. 期末大作业中，各种类型的复杂地形配比为：
    ```python
    terrain_dict = {
        "smooth slope": 0.25,
        "rough slope": 0.25,
        "rough stairs up": 0.1, 
        "rough stairs down": 0.1, 
        "discrete": 0.1, 
        "stepping stones": 0.1,
        "gaps": 0.1,
        }
    ```
2. 本次期末大作业中所使用的 Unitree G1 为 23 自由度版本，相比前面三次作业增加了上半身共计 11 个关节自由度。此外，代码中还增加了全尺寸人形机器人 Unitree H1，通过在训练脚本中切换参数 `--task=h1` 即可完成任务切换
3. 为实现全身仿人行走，需要加入上半身关节的运动约束，`legged_gym/envs/h1/h1.py` 和 `legged_gym/envs/g1/g1.py` 中展示了如何通过奖励实现关节位置的对称性约束，同学们也可以探索其他约束运动的方式（例如将上下半身运动解耦）

### 提交说明 ###
1. 策略权重导出与提交：选定合适的 checkpoint 后，运行脚本
    ```shell
    python legged_gym/legged_gym/scripts/play.py \
    --task=g1 \
    --run_name=<run_name> \
    --checkpoint=<checkpoint_id>
    ```
    预览策略表现，并以 onnx 格式导出策略，导出后需要将策略需要调整位于 `logs/exported` 目录下，并且需要统一更名为 `policy.pt`。同学们可以通过运行以下命令确认模型文件没有问题
    ```shell
    python legged_gym/legged_gym/scripts/eval_onnx.py --task=g1
    ```
2. 环境文件提交：考虑到全身运动可能涉及到模型观测和力矩计算等更改，需要提交更改后的 `envs/g1`或`envs/h1` 文件夹，例如其中环境文件 `g1.py` 和 `g1_config.py` 可以带有自定义的观测变量，自定义奖励及权重设置

    **请勿随意修改 urdf ，可能会出现仿真问题，影响大作业评分**

### 评分说明 ###

成功跨越地形区块的定义：机器人在回合终止前的行走距离 > 区块边界距离的一半

1. 任务评分（60 %）：
    
    G1 低速行走（30 %）：
    * xy 轴方向线速度 [0.0, 1.0] m/s
    * yaw 轴方向 [0.0, 0.5] m/s
    * 速度跟踪误差不超过 0.25 m/s
    * 平均存活时间超过 10 s 或成功跨越区块

    H1 行走（20 %）：
    * xy 轴方向线速度 [0.0, 1.0] m/s
    * yaw 轴方向 [0.0, 0.5] m/s
    * 速度跟踪误差不超过 0.25 m/s
    * 平均存活时间超过 10 s 或成功跨越区块

    G1 或 H1 高速行走（10 %）：
    * 选择 G1 或 H1
    * xy 轴方向线速度 [1.5, 2.0] m/s
    * yaw 轴方向 [0.0, 0.5] m/s
    * 速度跟踪误差不超过 0.5 m/s
    * 平均存活时间超过 10 s 或成功跨越区块

2. 地形评分（30 %）：

    跨越 `smooth slope`、`rough slope`、`stairs up`, `stairs down`、`discrete`、`stepping stones` 和 `gaps` 共计 7 种地形，每种地形共有 10 级难度。每成功跨越一个 `stairs up` 或者 `stairs down` 难度区块得 0.25 分，成功跨越其他地形的难度区块得 0.5 分

3. 风格评分（10 %）：

    上半身风格奖励 （5 %）：
    * 评测过程中 shoulder、elbow 等部位的 pitch 关节的位置的平均误差小于 0.5 rad
        
    下半身风格奖励 （5 %）：
    * 评测过程中 hip ankle 部位的 pitch 关节的位置的平均误差小于 0.8 rad