#!/usr/bin/env python3
"""
测试观测维度修复是否有效
"""

import sys
import os
import torch

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'legged_gym', 'legged_gym'))

def test_obs_dimensions():
    """测试观测维度"""
    print("测试 G1 观测维度修复...")
    
    try:
        from envs.g1.g1_config import G1RoughCfg
        from envs.g1.g1 import G1Rough
        
        # 创建配置
        cfg = G1RoughCfg()
        cfg.env.num_envs = 4  # 小规模测试
        
        print(f"配置的观测维度: {cfg.env.num_observations}")
        
        # 尝试创建环境（这里可能会失败，但我们主要关心观测维度）
        try:
            env = G1Rough(cfg=cfg, sim_params=None, physics_engine=None, sim_device="cpu", headless=True)
            
            # 测试观测计算
            obs, _ = env.reset()
            actual_obs_dim = obs.shape[1]
            
            print(f"实际观测维度: {actual_obs_dim}")
            print(f"观测形状: {obs.shape}")
            
            if actual_obs_dim == cfg.env.num_observations:
                print("✓ 观测维度匹配成功!")
                return True
            else:
                print(f"❌ 观测维度不匹配! 差异: {actual_obs_dim - cfg.env.num_observations}")
                return False
                
        except Exception as e:
            print(f"环境创建失败: {e}")
            print("这可能是由于缺少 Isaac Gym 环境，但配置检查已完成")
            return None
            
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def check_terrain_features():
    """检查地形特征相关代码"""
    print("\n检查地形特征实现...")
    
    # 检查基类中的地形特征计算
    try:
        from envs.base.legged_robot import LeggedRobot
        
        # 检查方法是否存在
        if hasattr(LeggedRobot, '_compute_terrain_features'):
            print("✓ _compute_terrain_features 方法存在于基类")
        else:
            print("❌ _compute_terrain_features 方法不存在于基类")
            
        # 检查 G1 的观测方法
        from envs.g1.g1 import G1Rough
        if hasattr(G1Rough, 'compute_observations'):
            print("✓ G1 重写了 compute_observations 方法")
        else:
            print("❌ G1 没有重写 compute_observations 方法")
            
        return True
        
    except Exception as e:
        print(f"检查失败: {e}")
        return False

def main():
    print("=" * 60)
    print("G1 观测维度修复测试")
    print("=" * 60)
    
    # 检查地形特征实现
    terrain_ok = check_terrain_features()
    
    # 测试观测维度
    obs_ok = test_obs_dimensions()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"地形特征检查: {'✓ 通过' if terrain_ok else '❌ 失败'}")
    
    if obs_ok is True:
        print("观测维度测试: ✓ 通过")
        print("\n🎉 修复成功！现在可以开始训练了。")
    elif obs_ok is False:
        print("观测维度测试: ❌ 失败")
        print("\n需要进一步调试观测维度问题。")
    else:
        print("观测维度测试: ⚠️ 无法完全验证（可能缺少运行环境）")
        print("\n配置看起来正确，建议在实际训练环境中测试。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
