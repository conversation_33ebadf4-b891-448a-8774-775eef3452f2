# SPDX-FileCopyrightText: Copyright (c) 2021 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
# 
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# Copyright (c) 2021 ETH Zurich, Nikita Rudin

from isaacgym import gymtorch, gymapi, gymutil

import os
import time
import torch
import numpy as np

from legged_gym.envs import LeggedRobot


class G1Rough(LeggedRobot):
    def compute_observations(self):
        """ Computes observations
        """
        self.obs_buf = torch.cat((
            self.commands[:, 0:3] * self.commands_scale,
            self.base_lin_vel * self.obs_scales.lin_vel,
            self.base_ang_vel  * self.obs_scales.ang_vel,
            self.projected_gravity,
            (self.dof_pos - self.default_dof_pos) * self.obs_scales.dof_pos,
            self.dof_vel * self.obs_scales.dof_vel,
            self.actions,
            ), dim=1)
        
        # add perceptive inputs if not blind
        if self.cfg.terrain.measure_heights:
            heights_wrt_root = self.root_states[:, 2:3] - self.measured_heights
            heights = heights_wrt_root * self.obs_scales.height_measurements
            self.obs_buf = torch.cat((self.obs_buf, heights), dim=1)

    #------------ reward functions----------------
    def _reward_joint_symmetry(self):
        # shoulder roll & yaw joints regularization around 0
        # shoulder pitch joint symmetry
        shoulder_roll_yaw_indices = [14, 15, 17, 19, 20, 22]
        shoulder_pitch_indices = [13, 16, 18, 21]
        
        joint_roll_yaw_error = torch.sum(torch.square(self.dof_pos[:, shoulder_roll_yaw_indices]), dim=-1)
        joint_pitch_error = torch.sum(torch.square(self.dof_pos[:, shoulder_pitch_indices]), dim=-1)
        
        error = torch.exp(-1.0 * joint_roll_yaw_error / self.cfg.rewards.tracking_sigma)
        error = error + torch.exp(-1.0 * joint_pitch_error / self.cfg.rewards.tracking_sigma)
        return error / 2.0

    def _reward_terrain_slope_penalty(self):
        """ 基于地形坡度的惩罚奖励

        在陡峭坡度上给予负向奖励，鼓励机器人在平坦地形上行走，
        或在坡度较大时采用更保守的动作。

        Returns:
            torch.Tensor: 坡度惩罚值，形状为 [num_envs]
        """
        # 检查是否有地形特征可用
        if not hasattr(self, 'terrain_features') or "slope_mag" not in self.terrain_features:
            return torch.zeros(self.num_envs, dtype=torch.float, device=self.device)

        # 获取归一化的坡度幅值 [0, 1]
        slope_mag = self.terrain_features["slope_mag"].squeeze(-1)  # [num_envs]

        # 使用平方函数作为惩罚：坡度越大，惩罚越重
        # 这里返回正值，因为在配置中会设置负的scale来实现惩罚
        penalty = torch.square(slope_mag)

        return penalty

    def _reward_stand_still(self):
        """ 静止奖励：当指令速度为零时，奖励机器人保持静止

        设计思路：
        1. 检测指令是否为零速度（线速度和角速度都接近零）
        2. 在零速度指令下，奖励机器人实际速度也接近零
        3. 使用指数衰减函数，速度越小奖励越大

        Returns:
            torch.Tensor: 静止奖励值，形状为 [num_envs]
        """
        # 定义"零速度"的阈值
        zero_cmd_threshold = 0.1  # 指令速度小于此值认为是零指令

        # 检测是否为零速度指令
        lin_cmd_magnitude = torch.norm(self.commands[:, :2], dim=1)  # xy线速度指令幅值
        ang_cmd_magnitude = torch.abs(self.commands[:, 2])  # z角速度指令幅值

        # 判断是否为静止指令（线速度和角速度指令都很小）
        is_stand_still_cmd = (lin_cmd_magnitude < zero_cmd_threshold) & (ang_cmd_magnitude < zero_cmd_threshold)

        # 计算实际速度幅值
        actual_lin_vel = torch.norm(self.base_lin_vel[:, :2], dim=1)  # 实际xy线速度
        actual_ang_vel = torch.abs(self.base_ang_vel[:, 2])  # 实际z角速度

        # 计算总的速度误差（线速度 + 角速度）
        total_vel_error = actual_lin_vel + actual_ang_vel

        # 使用指数函数奖励低速度（速度越小奖励越大）
        # 使用与 tracking 奖励相同的 sigma 参数保持一致性
        stand_still_reward = torch.exp(-total_vel_error / self.cfg.rewards.tracking_sigma)

        # 只在静止指令时给予奖励，其他时候为零
        reward = torch.where(is_stand_still_cmd, stand_still_reward, torch.zeros_like(stand_still_reward))

        return reward