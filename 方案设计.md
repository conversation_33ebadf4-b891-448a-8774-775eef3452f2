### 人形机器人全身仿人行走项目分工表

**核心原则：** 聚焦各自专长，确保高难度任务（地形）有充足的资源，同时保持成员间低耦合，高效并行。

| 负责人     | 核心角色               | 主要负责评分项 (占比)                         | 核心任务                                                     | 主要工作文件                                                 |
| ---------- | ---------------------- | --------------------------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **成员 A** | **G1行走与风格工程师** | **任务评分 (G1部分) (40%)**<br>               | 1. **G1低速行走:** 搭建稳定、可控的G1基础行走模型。<br>2. **G1高速行走:** 在低速模型基础上，挑战更高速度。<br>3. **风格奖励:** 负责上下半身关节的姿态约束奖励，让动作更自然、拟人。 | `legged_gym/envs/g1/g1.py`<br>`legged_gym/envs/g1/g1_config.py` |
| **成员 B** | **地形穿越专家**       | **地形评分 (30%)**<br>**风格评分 (10%)**      | 1. **地形感知:** 核心任务！研究并实现对前方地形的高度扫描。<br>2. **观测空间设计:** 将地形信息加入到机器人的观测中。<br>3. **适应性调试:** 与A和C合作，调试策略以利用地形信息，攻克楼梯、台阶等难点。 | `legged_gym/envs/g1/g1.py`<br>`legged_gym/envs/h1/h1.py`<br>`legged_gym/envs/base/legged_robot.py` |
| **成员 C** | **H1移植与总集成员**   | **任务评分 (H1部分) (20%)<br>全局训练与评估** | 1. **H1低速行走:** 搭建稳定、可控的H1基础行走模型<br>2. **H1高速行走:** 在低速模型基础上，挑战更高速度。<br>3. **评估与提交:** 对最终模型进行量化打分，选出最优版本，并负责打包提交。 | `legged_gym/envs/h1/h1.py`<br>`legged_gym/envs/h1/h1_config.py`<br>各类训练和评估脚本 |



#### **第一阶段：基础奠定 (并行启动)**



- **成员 A:** 启动G1项目，目标是快速实现一个**不要求风格、只要求稳定**的基础行走模型（能初步完成低速任务）。
- **成员 B:** **(关键)** 不用等A完成。从项目第一天起，就**并行开始研究**：
  1. 扩展 g1.py 和 h1.py 中的 `compute_observations`方法，确保所有必要的机器人状态（包括上半身关节状态）都被观测到
  2. 将这些高度信息整理成有效的观测（Observation）向量
  3. 查阅相关论文或资料，看成功的项目是如何解决这个问题的。
- **成员 C:**  启动H1项目，能初步完成低速任务



#### **第二阶段：核心功能开发 (分工明确)**



- **成员 A:** 在基础行走模型之上，开始**增加风格奖励**，并与C一起合作向**G1高速行走**发起冲击。他的工作是让G1“走得又快又帅”。
- **成员 B:** 实现**地形观测模块**。完成后，他会向A/C提供一个“地形补丁”，与A/C合作将该模块集成到G1环境中。
- **成员 C:** 在H1模型的基础上，与A合作开始**向H1模型的移植工作**，并随时准备接收和集成B的地形模块。



#### **第三阶段：集成、调试与优化 (协同作战)**



- **A和B的协同:** A的G1模型在集成了B的地形模块后，很可能在复杂地形上出现新的问题。此时A和B需要紧密合作，A可能需要调整奖励函数来鼓励机器人利用B提供的地形信息，而B可能也需要根据实际表现来调整地形观测的范围和精度。
- **B和C的协同:** C的H1模型在集成了B的地形模块后，B和C需要紧密合作，C需要调整奖励函数来鼓励机器人利用B提供的地形信息，而B可能也需要根据实际表现来调整地形观测的范围和精度。

---

#### 成员分工

A： 金泊宇

B： 卓俊豪

C： 张家畅

