---
title: IssacGym：平地机器人行走实验

---

### 项目分工表

**核心原则：** 聚焦各自专长，确保高难度任务（地形）有充足的资源，同时保持成员间低耦合，高效并行。

| 负责人     | 核心角色               | 主要负责评分项 (占比)                         | 核心任务                                                     | 主要工作文件                                                 |
| ---------- | ---------------------- | --------------------------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **成员 A** | **G1行走与风格工程师** | **任务评分 (G1部分) (40%)**<br>               | 1. **G1低速行走:** 搭建稳定、可控的G1基础行走模型。<br>2. **G1高速行走:** 在低速模型基础上，挑战更高速度。<br>3. **风格奖励:** 负责上下半身关节的姿态约束奖励，让动作更自然、拟人。 | `legged_gym/envs/g1/g1.py`<br>`legged_gym/envs/g1/g1_config.py` |
| **成员 B** | **地形穿越专家**       | **地形评分 (30%)**<br>**风格评分 (10%)**      | 1. **地形感知:** 核心任务！研究并实现对前方地形的高度扫描。<br>2. **观测空间设计:** 将地形信息加入到机器人的观测中。<br>3. **适应性调试:** 与A和C合作，调试策略以利用地形信息，攻克楼梯、台阶等难点。 | `legged_gym/envs/g1/g1.py`<br>`legged_gym/envs/h1/h1.py`<br>`legged_gym/envs/base/legged_robot.py` |
| **成员 C** | **H1移植与总集成员**   | **任务评分 (H1部分) (20%)<br>全局训练与评估** | 1. **H1低速行走:** 搭建稳定、可控的H1基础行走模型<br>2. **H1高速行走:** 在低速模型基础上，挑战更高速度。<br>3. **评估与提交:** 对最终模型进行量化打分，选出最优版本，并负责打包提交。 | `legged_gym/envs/h1/h1.py`<br>`legged_gym/envs/h1/h1_config.py`<br>各类训练和评估脚本 |



#### **第一阶段：基础奠定 (并行启动)**



- **成员 A:** 启动G1项目，目标是快速实现一个**不要求风格、只要求稳定**的基础行走模型（能初步完成低速任务）。
- **成员 B:** **(关键)** 不用等A完成。从项目第一天起，就**并行开始研究**：
  1. 扩展 g1.py 和 h1.py 中的 `compute_observations`方法，确保所有必要的机器人状态（包括上半身关节状态）都被观测到
  2. 将这些高度信息整理成有效的观测（Observation）向量
  3. 查阅相关论文或资料，看成功的项目是如何解决这个问题的。
- **成员 C:**  启动H1项目，能初步完成低速任务



#### **第二阶段：核心功能开发 (分工明确)**



- **成员 A:** 在基础行走模型之上，开始**增加风格奖励**，并与C一起合作向**G1高速行走**发起冲击。他的工作是让G1“走得又快又帅”。
- **成员 B:** 实现**地形观测模块**。完成后，他会向A/C提供一个“地形补丁”，与A/C合作将该模块集成到G1环境中。
- **成员 C:** 在H1模型的基础上，与A合作开始**向H1模型的移植工作**，并随时准备接收和集成B的地形模块。



#### **第三阶段：集成、调试与优化 (协同作战)**



- **A和B的协同:** A的G1模型在集成了B的地形模块后，很可能在复杂地形上出现新的问题。此时A和B需要紧密合作，A可能需要调整奖励函数来鼓励机器人利用B提供的地形信息，而B可能也需要根据实际表现来调整地形观测的范围和精度。
- **B和C的协同:** C的H1模型在集成了B的地形模块后，B和C需要紧密合作，C需要调整奖励函数来鼓励机器人利用B提供的地形信息，而B可能也需要根据实际表现来调整地形观测的范围和精度。

---

#### 成员分工

A： 金泊宇

B： 卓俊豪

C： 张家畅


### 项目协作
#### github
将关键文件上传到仓库，根据分工建立不同的分支
- 各成员在自己的分支上探索参数与函数的效果
- 当确定部分实现具有显著价值时，以“feat”前缀commit，供其他成员检验效果并选择是否合并


#### hackmd
使用`hackmd`共享文档，共享实验过程中的参数设置与奖励设计等工作的经验，同时可以记录过程中的工作日志（方便后续汇报展示时收集材料）




### 摘记

**老师的意见**
- 利用`legged_robot`中现有的奖励（很多还没有用上）来约束机器人的姿态
    - 参考 `legged_robot`的实现，调整 `legged_robot_config`中的`class rewards`内部的参数
- 对于上本身的约束，目标是让各个关节位置近似于0
- 在 `g1.py`中增加了observations的维度，需要在 `class env( LeggedRobotCfg.env )`中的 `num_observations`同步更新
- 不要一次性调整太多参数；如果可能，考虑多卡训练或者一卡同时训练两个进程来加速


**同学的经验**
- 可视化时，可以修改地形的组成，可以是先后简单和复杂的地形，分别观测效果；


#### 其他

- SMAC-实验第二课的[直播回放](https://interactivemeta.cmc.zju.edu.cn/#/replay?course_id=72470&sub_id=1548152&tenant_code=112)
    - 但是助教没有用话筒,完全听不清楚orz
- Gemini cli分析得到的参数含义和调整策略


![image](https://hackmd.io/_uploads/ByV0Y8srlx.png)
> 参数含义

![image](https://hackmd.io/_uploads/r1sxqLoree.png)
> 调整策略

![image](https://hackmd.io/_uploads/B15Z9Liree.png)
> 日志参数

![image](https://hackmd.io/_uploads/S1o7qUoHlg.png)
> 主要调整的参数

#### robot_legged中的scales
   1. `termination = -0.0`
       * 作用：回合终止惩罚。当机器人因摔倒（例如，接触力过大或姿态失衡）而提前终止回合时，会施加此惩罚。
       * 调整策略：
           * 通常设置为一个较小的负值（例如 -1.0 或 -5.0），以鼓励机器人避免摔倒，延长存活时间。
           * 如果设置为 0.0，则机器人不会因为摔倒而直接受到惩罚，可能导致它更频繁地摔倒。
           * 如果惩罚过大，机器人可能会变得过于保守，不敢探索或移动。


   2. `tracking_lin_vel = 1.0`
       * 作用：线性速度跟踪奖励。奖励机器人实际线性速度（XY平面）与目标线性速度的匹配程度。值越高，机器人越能精确地跟踪目标速度。
       * 调整策略：
           * 这是核心任务奖励，通常设置为较高的正值（例如 1.0 或更高），以驱动机器人向前移动和跟踪速度指令。
           * 如果机器人不动或速度很慢，可以尝试增加此值。
           * 如果过高，可能导致机器人为了速度而牺牲稳定性或平滑性。


   3. `tracking_ang_vel = 0.5`
       * 作用：角速度跟踪奖励。奖励机器人实际绕 Z 轴（yaw）角速度与目标角速度的匹配程度。
       * 调整策略：
           * 这是另一个核心任务奖励，通常设置为正值。
           * 如果机器人无法有效转向或出现不必要的旋转，可以尝试增加此值。
           * 与 tracking_lin_vel 配合调整，平衡直线行走和转向能力。


   4. `lin_vel_z = -2.0`
       * 作用：Z 轴线性速度惩罚。惩罚机器人基础在垂直方向（Z轴）的线性速度。
       * 调整策略：
           * 这是摔倒惩罚的一部分，通常设置为负值。
           * 增加其绝对值（例如 -5.0 或 -10.0）可以使机器人更平稳，减少跳动或下沉。
           * 如果惩罚过大，机器人可能不敢抬腿，导致步态僵硬或无法移动。


   5. `ang_vel_xy = -0.05`
       * 作用：XY 轴角速度惩罚。惩罚机器人基础在 X 和 Y 轴（roll 和 pitch）上的角速度。
       * 调整策略：
           * 这是摔倒惩罚的另一部分，通常设置为负值。
           * 增加其绝对值（例如 -0.1 或 -0.5）可以使机器人更稳定，减少左右摇晃和前后俯仰。
           * 如果惩罚过大，机器人可能过于僵硬，难以适应地形变化。


   6. `orientation = -0.0`
       * 作用：姿态惩罚。惩罚机器人基础偏离期望姿态（通常是保持水平）的程度。
       * 调整策略：
           * 通常设置为负值。
           * 增加其绝对值（例如 -1.0 或 -5.0）可以鼓励机器人保持直立和平衡。
           * 对于复杂地形，可能需要允许一定的倾斜，所以不宜过大。


   7. `torques = -0.00001`
       * 作用：力矩惩罚。惩罚机器人关节输出的力矩大小。
       * 调整策略：
           * 通常设置为较小的负值，鼓励节能和减少电机过载。
           * 增加其绝对值（例如 -0.0001）可以使机器人动作更“轻柔”，减少抖动，但过高可能导致机器人力量不足，无法完成任务。


   8. `dof_vel = -0.0`
       * 作用：关节速度惩罚。惩罚机器人关节的运动速度。
       * 调整策略：
           * 通常设置为负值。
           * 增加其绝对值（例如 -0.01 或 -0.05）可以使关节运动更平滑，减少冲击，但过高可能导致机器人动作迟缓。


   9. `dof_acc = -2.5e-7`
       * 作用：关节加速度惩罚。惩罚机器人关节的加速度。
       * 调整策略：
           * 通常设置为较小的负值，鼓励平滑的关节运动，减少机械磨损。
           * 增加其绝对值（例如 -1.0e-6）可以使动作更流畅，但过高可能导致机器人反应迟钝。


   10. `base_height = -0.0`
       * 作用：基础高度惩罚。惩罚机器人基础高度偏离目标高度的程度。
       * 调整策略：
           * 通常设置为负值。
           * 增加其绝对值（例如 -1.0 或 -5.0）可以鼓励机器人保持在期望的高度，避免蹲下或站得过高。
           * base_height_target (在 rewards 类下) 定义了目标高度。


   11. `feet_air_time = 1.0`
       * 作用：脚部空中时间奖励。奖励机器人脚部在空中停留的时间。
       * 调整策略：
           * 通常设置为正值，鼓励机器人迈大步，形成更自然的步态，而不是小碎步。
           * 增加此值可以使机器人步幅更大，但过高可能导致机器人难以保持平衡。


   12. `collision = -1.0`
       * 作用：碰撞惩罚。惩罚机器人特定身体部位（在 asset.penalize_contacts_on 中定义）与环境的碰撞。
       * 调整策略：
           * 通常设置为负值。
           * 增加其绝对值（例如 -5.0 或 -10.0）可以强烈阻止机器人与地面或其他障碍物发生不期望的接触。


   13. `feet_stumble = -0.0`
       * 作用：脚部绊倒惩罚。惩罚脚部撞到垂直表面（例如台阶边缘）的情况。
       * 调整策略：
           * 通常设置为负值。
           * 增加其绝对值（例如 -1.0 或 -5.0）可以鼓励机器人更小心地跨越障碍，避免绊倒。


   14. `action_rate = -0.01`
       * 作用：动作变化率惩罚。惩罚机器人连续时间步之间动作指令的剧烈变化。
       * 调整策略：
           * 通常设置为负值，鼓励平滑、连续的动作。
           * 增加其绝对值（例如 -0.05）可以使动作更平滑，但过高可能导致机器人反应迟钝或无法快速适应。


   15. `stand_still = -0.0`
       * 作用：静止惩罚。当机器人没有收到移动指令（即 commands 为零）时，惩罚其不必要的运动。
       * 调整策略：
           * 通常设置为负值。
           * 增加其绝对值（例如 -0.1 或 -0.5）可以鼓励机器人在没有指令时保持完全静止。




