### 人形机器人全身仿人行走项目 - 成员 B (卓俊豪) 工作计划 (V2)

#### 1. 对原《方案设计.md》的分析和存在的问题

原《方案设计.md》为项目提供了初步的分工和阶段性目标，但在细节和职责划分上存在一些可以改进的地方：

*   **风格评分职责重叠**：方案中将“风格评分 (10%)”分配给成员 B，同时成员 A 的核心任务包含“风格奖励”。这可能导致职责不清，需要明确成员 B 在风格评分中的具体角色是评估和验证，而成员 A 负责实现奖励函数。
*   **地形感知和观测空间设计缺乏具体技术方案**：虽然明确了成员 B 负责地形感知和观测空间设计，但未提供具体的技术路线或实现细节，例如如何进行高度扫描、如何提取地形特征等。
*   **“地形补丁”交付的独立性和可集成性**：方案中提到成员 B 将提供“地形补丁”给 A/C 集成。这需要成员 B 在开发时充分考虑模块的独立性、接口的清晰性以及与现有代码库的无缝集成，避免后期出现大量兼容性问题。
*   **上半身关节观测的必要性及实现细节**：方案中要求成员 B 扩展 `compute_observations` 方法以观测上半身关节状态。需要明确这些上半身信息对地形穿越的必要性，以及如何在观测空间中有效地表示这些高维数据。

#### 2. 作为成员 B 的核心职责和任务

根据项目需求和现有代码分析，作为成员 B（卓俊豪），我的核心职责是**地形穿越专家**，主要任务围绕地形感知、观测空间设计和策略适应性调试展开，并负责风格评分的评估。

**核心职责：**

*   **地形感知与观测空间设计**：使机器人能够感知并理解其所处地形的复杂性，并将这些信息有效地融入强化学习的观测空间。
*   **适应性调试**：与成员 A 和 C 紧密合作，调试和优化策略，使机器人能够利用地形信息成功穿越各种复杂地形。
*   **风格评分评估**：负责对机器人全身仿人行走的风格进行量化评估，确保动作的自然性和拟人化。

**主要任务：**

1.  **地形感知模块开发**：实现对前方地形的高度扫描，并从原始高度数据中提取有意义的地形特征（如坡度、粗糙度、障碍物边缘信息）。
2.  **观测空间扩展**：将地形特征信息和上半身关节状态（位置和速度）添加到 G1 和 H1 机器人的观测向量中。
3.  **地形适应性策略调试**：与 A 和 C 合作，根据地形观测信息调整和优化强化学习策略，以提高机器人在楼梯、台阶、不规则地面等复杂地形上的行走能力。
4.  **风格评分实现与评估**：理解并扩展现有的风格奖励机制，设计量化指标来评估上半身和下半身关节的姿态约束，确保动作的自然拟人。

#### 3. 具体的技术实现方案

*   **地形观测**：
    *   **优化采样点**：在 `legged_gym/envs/base/legged_robot_config.py` 中，调整 `terrain.measured_points_x` 和 `terrain.measured_points_y` 的值，增加采样点的密度或调整采样范围，以更好地捕捉复杂地形的细节。例如，可以增加采样点的数量，或者在机器人前方更远的距离进行采样。
    *   **提取高级地形特征**：在 `legged_gym/envs/base/legged_robot.py` 的 `_get_heights` 方法中，除了原始高度值，还可以计算相邻采样点的高度差来表示坡度，或者计算局部高度的标准差来表示粗糙度。这些高级特征将作为新的观测维度加入到 `obs_buf` 中。

*   **上半身关节观测**：
    *   **G1 机器人**：在 `legged_gym/envs/g1/g1.py` 的 `compute_observations` 方法中，已经添加了 G1 上半身关节（`waist_yaw_joint`、`left_shoulder_yaw_joint` 等）的位置和速度到观测空间。需要确保这些关节的索引与 `g1_config.py` 中的 `default_joint_angles` 顺序一致，并验证观测维度是否正确更新。
    *   **H1 机器人**：在 `legged_gym/envs/h1/h1.py` 的 `compute_observations` 方法中，同样需要添加 H1 上半身关节（`torso_joint`、`left_shoulder_yaw_joint` 等）的位置和速度到观测空间。 `h1_config.py` 中的 `default_joint_angles` 顺序一致，并验证观测维度。
    *   **观测维度管理**：由于增加了新的观测维度，需要相应地更新 `g1_config.py` 和 `h1_config.py` 中 `env.num_observations` 的值，以匹配实际的观测空间大小。

*   **风格奖励**：
    *   **理解现有实现**：深入分析 `g1.py` 和 `h1.py` 中 `_reward_joint_symmetry` 函数的实现，理解其如何计算关节对称性误差以及如何将其转化为奖励。
    *   **与成员 A 协作**：与成员 A 讨论并共同设计更全面的风格奖励函数。这可能包括：
        *   **姿态匹配奖励**：鼓励机器人上半身关节保持特定姿态（例如，在行走时保持躯干直立，手臂自然摆动）。
        *   **平滑度奖励**：惩罚关节运动的剧烈变化，鼓励平滑自然的动作。
        *   **平衡性奖励**：鼓励机器人保持良好的平衡，减少不必要的晃动。
    *   **评估指标**：设计量化指标来评估上半身和下半身关节的姿态约束，例如计算关节位置的平均误差、关节速度的平滑度等，用于最终的风格评分。

#### 4. 与成员 A 和 C 的协作点

*   **地形观测模块的集成**：在地形观测模块开发完成后，我将提供一个清晰的接口或“补丁”，与成员 A 和 C 合作将其集成到 G1 和 H1 的环境中。这可能涉及到修改 `legged_robot.py` 或其子类中的 `compute_observations` 方法。
*   **策略调试与地形信息利用**：在训练过程中，当机器人遇到复杂地形时，我将与 A 和 C 紧密合作，分析机器人的行为，并根据地形观测信息调整奖励函数或策略参数，以鼓励机器人更好地利用地形信息进行穿越。
*   **风格奖励的共同设计与评估**：与成员 A 共同设计和迭代风格奖励函数，确保其能够有效地引导机器人学习到拟人化的行走风格。同时，与 A 和 C 共同进行风格评分的评估，确保评估标准的一致性和准确性。

#### 5. 观测维度使用与奖励函数集成

**5.1 观测（Observations）的作用**

观测是机器人感知环境和自身状态的输入。强化学习代理（Policy）会根据这些观测来决定下一步的动作。新的地形特征和上半身关节状态作为观测，将直接提供给策略网络，使其能够获取更全面的环境和自身信息，从而做出更智能的决策。

**5.2 奖励（Rewards）的作用**

奖励是指导机器人学习的信号。通过设计合适的奖励函数，我们可以鼓励机器人执行期望的行为，并惩罚不期望的行为。将观测信息融入奖励函数，可以更直接地引导策略学习特定的行为模式。

**5.3 具体如何使用新增观测**

*   **直接用于策略学习**：
    *   一旦新的地形特征（如坡度、粗糙度）和上半身关节位置/速度被添加到 `obs_buf` 中，策略网络（通常是神经网络）会自动接收这些信息。
    *   策略网络会通过训练学习如何利用这些新的观测来改进其决策，例如，根据地形的坡度调整步态，或者利用上半身姿态来维持平衡。
    *   **重要提示**：在添加新的观测维度后，务必更新 `g1_config.py` 和 `h1_config.py` 中 `env.num_observations` 的值，使其与实际的观测向量长度匹配。否则，策略网络将无法正确解析输入。

*   **加入到奖励函数中（可选但推荐）**：
    *   虽然策略可以自行从观测中学习，但将关键的观测信息融入奖励函数可以更有效地引导学习过程，尤其是在您希望机器人表现出特定行为时。
    *   **地形相关奖励**：
        *   **地形适应性奖励**：可以根据机器人成功穿越不同难度地形的距离或时间给予奖励。例如，在陡峭的坡道上保持稳定姿态，或者成功跨越障碍物时给予额外奖励。
        *   **地形惩罚**：如果机器人与地形发生不期望的碰撞（例如，身体而非脚部接触地面），或者在复杂地形上摔倒，可以给予惩罚。
        *   **高度保持奖励**：鼓励机器人在复杂地形上保持一个相对稳定的躯干高度，避免剧烈起伏。
    *   **上半身风格奖励**：
        *   **姿态约束奖励**：如《方案设计.md》和现有代码所示，可以对上半身关节（如肩部、肘部、腰部）的姿态进行约束，使其保持在合理范围内，或者鼓励对称性运动。这可以通过计算关节位置与目标姿态的误差来惩罚。
        *   **运动平滑度奖励**：惩罚关节运动的剧烈变化，鼓励平滑自然的动作。
        *   **平衡辅助奖励**：如果上半身运动对维持平衡有帮助，可以设计奖励来鼓励这种协调运动。

**5.4 总结**

新的观测维度是策略学习的基础输入，而将其融入奖励函数则是加速和引导学习过程的有效手段。作为成员 B，您需要：

1.  **确保观测维度正确添加并更新配置。**
2.  **根据项目目标，设计并实现相应的地形和上半身风格奖励函数，以引导机器人学习期望的行为。**

**5.5 奖励函数示例：基于地形坡度的惩罚**

假设您已经在 `legged_gym/envs/base/legged_robot.py` 的 `_get_heights` 方法中计算并添加了地形的坡度信息到观测中（例如，`self.measured_slopes`）。现在，我们可以在奖励函数中利用这个信息来惩罚机器人在陡峭坡度上的不稳定行为。

**步骤 1：在配置中定义奖励尺度**

在 `legged_gym/envs/g1/g1_config.py` 或 `legged_gym/envs/h1/h1_config.py` 的 `rewards.scales` 类中，添加一个新的奖励尺度：

```python
class rewards( LeggedRobotCfg.rewards ):
    class scales:
        # ... 其他奖励尺度 ...
        # 新增的地形坡度惩罚
        terrain_slope_penalty = -0.1 # 惩罚系数，可根据需要调整
```

**步骤 2：在环境中实现奖励函数**

在 `legged_gym/envs/base/legged_robot.py` 或其子类（如 `g1.py`, `h1.py`）中，添加一个新的私有方法来计算这个奖励：

```python
# ... existing code ...

    def _reward_terrain_slope_penalty(self):
        # 假设 self.measured_slopes 包含了地形的坡度信息
        # 坡度值越大，惩罚越大
        # 可以根据实际情况调整惩罚函数，例如使用平方或指数函数
        # 这里假设 measured_slopes 是一个表示坡度绝对值的张量
        if self.cfg.terrain.measure_heights and hasattr(self, 'measured_slopes'):
            # 惩罚与坡度绝对值成正比
            # 可以根据需要调整阈值，只惩罚超过一定坡度的区域
            # 例如：torch.sum(torch.abs(self.measured_slopes).clip(min=0.5), dim=1)
            return torch.sum(torch.abs(self.measured_slopes), dim=1)
        else:
            return torch.zeros_like(self.rew_buf) # 如果没有坡度信息，则奖励为0

# ... existing code ...
```

**解释：**

*   `_reward_terrain_slope_penalty` 方法会检查是否启用了地形高度测量 (`self.cfg.terrain.measure_heights`) 并且 `self` 对象是否具有 `measured_slopes` 属性（这需要您在 `_get_heights` 方法中计算并存储坡度）。
*   `torch.sum(torch.abs(self.measured_slopes), dim=1)` 计算了每个环境中所有测量点坡度绝对值的和。这意味着坡度越大，惩罚越大。您可以根据实际需求调整惩罚函数，例如，只惩罚超过某个阈值的坡度，或者使用非线性惩罚。
*   这个奖励函数会在 `compute_reward` 方法中被调用，并乘以在配置中定义的 `terrain_slope_penalty` 尺度。

**如何将 `measured_slopes` 添加到 `_get_heights` 中：**

在 `legged_gym/envs/base/legged_robot.py` 的 `_get_heights` 方法中，您需要计算坡度。这通常涉及到计算相邻高度点之间的差值。例如：

```python
# ... existing code in _get_heights ...

        # 计算高度
        heights = torch.amin(torch.stack([heights1, heights2, heights3], dim=-1), dim=-1)
        measured_heights = heights.view(self.num_envs, -1) * self.terrain.cfg.vertical_scale

        # 示例：计算简单的坡度（这里只是一个示意，实际可能需要更复杂的计算）
        # 假设 measured_heights 是 (num_envs, num_height_points)
        # 您需要根据 measured_points_x 和 measured_points_y 的布局来计算 x 和 y 方向的坡度
        # 这里只是一个非常简化的例子，实际需要考虑网格结构
        if self.num_height_points > 1:
            # 沿x方向的坡度示例
            slopes_x = measured_heights[:, 1:] - measured_heights[:, :-1]
            # 沿y方向的坡度示例 (需要更复杂的索引或重塑)
            # slopes_y = ...
            self.measured_slopes = torch.abs(slopes_x) # 暂时只用x方向的坡度作为示例

        return measured_heights
```

通过以上步骤，您就可以将地形坡度信息作为观测的一部分，并将其融入到奖励函数中，从而引导机器人学习如何更好地应对复杂地形。

#### 6. 奖励配置与训练策略

**6.1 奖励尺度继承机制**

*   **覆盖机制**：在 `legged_gym` 的配置体系中，子类（如 `G1RoughCfg` 或 `H1RoughCfg`）中定义的 `rewards.scales` 会**覆盖**父类（`LeggedRobotCfg`）中同名奖励的定义。这意味着，如果 `LeggedRobotCfg` 中 `orientation = -0.0`，而 `G1RoughCfg` 中设置 `orientation = -0.2`，那么对于 G1 机器人，`orientation` 奖励将以 `-0.2` 的尺度生效。
*   **生效原理**：在环境初始化时，`LeggedRobot` 基类会根据当前加载的配置（即 `G1RoughCfg` 或 `H1RoughCfg` 的实例）来构建 `self.reward_scales` 字典。由于子类配置会覆盖父类配置，因此最终生效的是子类中定义的奖励尺度。

**6.2 设置零尺度奖励的策略**

当您希望激活一个当前尺度为零的奖励时，有两种主要策略：

*   **逐个设置（推荐用于初始设置和调试）**：
    *   **优点**：这种方法提供了最大的控制力，您可以清晰地观察每个新奖励项对机器人行为的影响。当出现问题时，更容易定位是哪个奖励项导致了问题。
    *   **操作**：在对应的机器人配置文件（如 `g1_config.py` 或 `h1_config.py`）中，找到您想要激活的奖励项，将其尺度从 `0.0` 修改为非零值（例如，一个小的负值用于惩罚，或正值用于奖励）。
    *   **建议**：在机器人能够执行基本运动（如平地行走）之后，再逐步引入新的奖励项。

*   **全部设置为非零然后逐个调节（不推荐用于初始设置）**：
    *   **缺点**：这种方法在初始阶段可能导致学习过程非常不稳定和混乱。如果所有奖励项都同时生效，并且它们的相对权重不合适，智能体可能会收到相互冲突的信号，导致难以收敛或学习到非预期的行为。
    *   **适用场景**：当您已经对所有奖励项的作用有很好的理解，并且机器人已经能够执行复杂任务，需要进行精细的超参数调优时，可以考虑这种方法。

**6.3 训练和调试策略**

为了高效地训练和调试机器人，建议采取以下策略：

1.  **从简入手，逐步增加复杂度**：
    *   **基础训练**：首先，使用一个包含基本奖励（如速度跟踪、姿态保持、关节力矩惩罚、跌倒惩罚）的配置，让机器人在平坦地形上学习稳定的行走。
    *   **地形适应性**：一旦基础行走稳定，再逐步引入地形相关的奖励（如坡度惩罚、高度保持奖励），并利用课程学习（`terrain.curriculum = True`）从简单地形开始训练，逐渐增加地形难度。
    *   **全身协调与风格**：最后，引入上半身关节相关的奖励（如姿态约束、运动平滑度），以引导机器人学习更拟人化的全身协调动作。

2.  **监控和可视化**：
    *   **奖励分解**：利用日志工具（如 TensorBoard）监控每个独立奖励项的贡献。这可以帮助您判断某个奖励是否过强或过弱，或者是否产生了意想不到的行为。
    *   **行为可视化**：定期使用 `play.py` 脚本在模拟器中可视化机器人的行为。这对于定性评估策略的学习效果至关重要，可以直观地发现问题（如步态不自然、在特定地形上卡住等）。

3.  **超参数调优**：
    *   **奖励尺度**：奖励尺度是关键的超参数。它们决定了每个奖励项在总奖励中的相对重要性。如果某个奖励尺度过大，可能会导致智能体过度优化该项而忽略其他重要行为；如果过小，则可能无法有效引导学习。
    *   **迭代调整**：根据监控数据和可视化结果，迭代地调整奖励尺度。通常从较小的非零值开始，然后根据需要逐渐增加或减少。

4.  **调试新观测**：
    *   如果您添加了新的观测维度（如地形特征、上半身关节状态），请确保它们被正确地归一化（通过 `obs_scales`）。
    *   在训练初期，可以打印或记录这些新观测的范围和分布，确保它们的值是合理的，并且没有出现 NaN 或 Inf 等异常情况。

5.  **Ablation Studies (消融研究)**：
    *   当您对某个奖励项的作用不确定时，可以进行消融研究：训练两个几乎相同的智能体，一个包含该奖励项，另一个不包含，然后比较它们的性能差异。这有助于量化每个奖励项的贡献。

#### 7. 评估方法与判断时机

为了有效地评估训练进度和判断何时停止训练，可以从以下几个方面进行考量：

**7.1 评估指标**

*   **任务性能指标（定量）**：
    *   **速度跟踪误差**：机器人实际线速度和角速度与指令速度的匹配程度。
    *   **平均存活时间**：机器人能够稳定运行而不跌倒的平均时间。
    *   **地形跨越成功率/距离**：在不同难度地形上成功跨越的区块数量或行走距离（如 `final_project.md` 中定义的“成功跨越地形区块的定义”）。
    *   **任务评分**：根据 `final_project.md` 中“评分说明”部分的任务评分标准进行量化打分。
*   **风格指标（定量与定性结合）**：
    *   **关节姿态误差**：上半身和下半身关键关节（如肩部、肘部、髋部、踝部）与目标姿态（例如，对称姿态或自然摆臂姿态）的平均误差。这可以通过您在奖励函数中使用的误差项来量化。
    *   **运动平滑度**：关节速度或加速度的方差，用于评估动作的流畅性。
    *   **视觉评估**：通过 `play.py` 脚本在模拟器中观察机器人的行走姿态，判断其是否自然、拟人。这是非常重要的定性评估方式。
*   **训练过程指标（定量）**：
    *   **总奖励曲线**：监控训练过程中每个回合的平均总奖励。通常，奖励曲线应该呈现上升趋势并最终趋于稳定。
    *   **各个奖励项的贡献**：分解总奖励，观察每个独立奖励项（如速度跟踪奖励、地形惩罚、风格奖励）的变化趋势。这有助于理解智能体正在学习什么，以及奖励函数是否有效。
    *   **价值损失 (Value Loss)** 和 **策略损失 (Policy Loss)**：这些是强化学习算法内部的损失函数，可以帮助判断训练是否收敛，以及是否存在过拟合或欠拟合。
    *   **KL 散度 (KL Divergence)**：在 PPO 等算法中，监控新旧策略之间的 KL 散度，确保策略更新的步长合适。

**7.2 判断时机**

*   **奖励曲线收敛**：当总奖励曲线趋于平稳，不再有显著提升时，可能表明策略已经收敛。
*   **任务性能达标**：根据项目要求，当机器人能够稳定地完成特定难度等级的任务（例如，达到一定的速度跟踪精度、平均存活时间或地形跨越成功率）时。
*   **风格表现满意**：通过可视化观察，机器人的全身动作看起来自然、协调，符合仿人行走的要求。
*   **验证集性能**：如果可能，将训练好的策略在独立的验证集（包含不同于训练地形的地形）上进行评估，以检查泛化能力。
*   **过拟合迹象**：如果训练奖励持续上升，但验证奖励开始下降，或者可视化显示机器人行为变得不稳定或出现奇怪的模式，这可能是过拟合的迹象，此时应停止训练或采取正则化措施。
*   **资源限制**：在实际项目中，训练时间或计算资源可能有限。在这种情况下，需要在性能和资源之间进行权衡。

**7.3 评估工具**

*   **TensorBoard**：`legged_gym` 通常会集成 TensorBoard 进行日志记录。您可以通过运行 `tensorboard --logdir=logs` 命令来启动 TensorBoard，并在浏览器中查看各种训练指标的曲线图。
*   **`legged_gym/legged_gym/scripts/play.py`**：如 `final_project.md` 中所述，使用此脚本加载训练好的策略模型，并在模拟器中实时观察机器人的行为。这是最直观的定性评估方式。
*   **`legged_gym/legged_gym/scripts/eval_onnx.py`**：此脚本用于导出 ONNX 格式的策略模型并进行评估。您可以修改此脚本，使其在特定地形上运行机器人，并记录任务性能指标（如行走距离、速度误差、存活时间等），从而进行量化评估。

通过结合定量指标和定性观察，您可以更全面地评估训练进度，并做出何时停止训练或调整策略的决策。