# G1 奖励函数实现总结

## 概述
基于 `legged_robot_config.py` 中的奖励配置，为 G1 机器人实现了完整的奖励函数体系。将原本设置为 0 的奖励项激活，以提供更精细的行为控制和训练信号。

## 实现的奖励函数

### 1. 地形相关奖励

#### `terrain_slope_penalty = -0.05`
- **函数**: `_reward_terrain_slope_penalty()`
- **位置**: `final_project/legged_gym/legged_gym/envs/g1/g1.py`
- **功能**: 基于地形坡度给予惩罚，鼓励在平坦地形行走
- **实现**: 
  - 计算局部坡度幅值
  - 使用平方函数增强惩罚效果
  - 坡度越大惩罚越重

### 2. 行为控制奖励

#### `stand_still = 0.5`
- **函数**: `_reward_stand_still()`
- **位置**: `final_project/legged_gym/legged_gym/envs/g1/g1.py`
- **功能**: 在零速度指令时奖励机器人保持静止
- **实现**:
  - 检测零速度指令（阈值 0.1）
  - 奖励实际速度接近零的行为
  - 使用指数函数，速度越小奖励越大

#### `joint_symmetry = 0.25`
- **函数**: `_reward_joint_symmetry()`
- **位置**: `final_project/legged_gym/legged_gym/envs/g1/g1.py`
- **功能**: 鼓励上半身关节的对称性和自然姿态
- **实现**:
  - 肩部 roll/yaw 关节正则化到 0
  - 肩部 pitch 关节对称性约束

### 3. 稳定性和安全奖励（新激活）

#### `orientation = -0.2`
- **函数**: `_reward_orientation()`（基类实现）
- **功能**: 惩罚机器人姿态偏离直立状态
- **实现**: 基于 `projected_gravity[:, :2]` 计算姿态偏差

#### `base_height = -0.1`
- **函数**: `_reward_base_height()`（基类实现）
- **功能**: 惩罚基座高度偏离目标值
- **实现**: 
  - 目标高度: `base_height_target = 0.25`
  - 惩罚偏离目标高度的行为

#### `dof_vel = -0.001`
- **函数**: `_reward_dof_vel()`（基类实现）
- **功能**: 惩罚关节速度过大
- **实现**: 对所有关节速度的平方和进行惩罚

#### `feet_stumble = -0.5`
- **函数**: `_reward_feet_stumble()`
- **位置**: `final_project/legged_gym/legged_gym/envs/g1/g1.py`
- **功能**: 惩罚脚部撞击垂直表面（绊倒）
- **实现**:
  - 检测水平接触力 > 5 × 垂直接触力
  - 任何一只脚绊倒都会触发惩罚

#### `termination = -1.0`
- **函数**: `_reward_termination()`（基类实现）
- **功能**: 惩罚非正常终止（如摔倒）
- **实现**: 基于 `reset_buf` 和 `time_out_buf` 判断终止原因

### 4. 已有的任务奖励（保持不变）

#### `tracking_lin_vel = 1.0`
- **功能**: 线速度跟踪奖励

#### `tracking_ang_vel = 0.5`
- **功能**: 角速度跟踪奖励

#### `lin_vel_z = -1.0`
- **功能**: 惩罚 z 轴线速度

#### `ang_vel_xy = -0.05`
- **功能**: 惩罚 xy 轴角速度

#### `torques = -0.0002`
- **功能**: 惩罚过大力矩

#### `dof_acc = -2.5e-7`
- **功能**: 惩罚关节加速度

#### `feet_air_time = 1.0`
- **功能**: 奖励长步幅

#### `collision = -1.0`
- **功能**: 惩罚碰撞

#### `action_rate = -0.01`
- **功能**: 惩罚动作变化率

#### `dof_pos_limits = -10.0`
- **功能**: 惩罚关节位置超限

## 观测空间扩展

### 新增观测维度
- **坡度特征**: 1 维
- **总观测维度**: 268 → 269

### 观测缩放参数
```python
class obs_scales:
    lin_vel = 2.0
    ang_vel = 0.25
    dof_pos = 1.0
    dof_vel = 0.05
    height_measurements = 5.0
    slope = 1.0  # 新增
```

## 配置文件更新

### `final_project/legged_gym/legged_gym/envs/g1/g1_config.py`
```python
class env:
    num_observations = 269  # 原 268 + 1 个坡度特征

class rewards:
    base_height_target = 0.25
    
    class scales:
        # task rewards
        tracking_lin_vel = 1.0
        tracking_ang_vel = 0.5
        joint_symmetry = 0.25
        
        lin_vel_z = -1.0
        ang_vel_xy = -0.05
        torques = -0.0002
        dof_acc = -2.5e-7
        feet_air_time = 1.0
        collision = -1.0
        action_rate = -0.01
        dof_pos_limits = -10.0
        
        # terrain-based rewards
        terrain_slope_penalty = -0.05
        
        # behavioral rewards
        stand_still = 0.5
        
        # stability and safety rewards (newly activated)
        orientation = -0.2
        base_height = -0.1
        dof_vel = -0.001
        feet_stumble = -0.5
        termination = -1.0
```

## 权重设计原理

### 正奖励（鼓励行为）
- `tracking_*`: 主要任务目标，权重较大
- `feet_air_time`: 鼓励自然步态
- `joint_symmetry`: 鼓励上半身对称
- `stand_still`: 鼓励静止控制

### 负奖励（惩罚行为）
- **强惩罚** (`-1.0` 以上): `termination`, `collision`, `dof_pos_limits`
- **中等惩罚** (`-0.1` 到 `-1.0`): `lin_vel_z`, `feet_stumble`, `orientation`
- **轻微惩罚** (`-0.1` 以下): `ang_vel_xy`, `base_height`, `terrain_slope_penalty`
- **微调惩罚** (`-0.01` 以下): `action_rate`, `dof_vel`, `torques`, `dof_acc`

## 训练建议

### 权重调优策略
1. **初期训练**: 可以将部分惩罚权重设为 0，专注于基本任务
2. **中期训练**: 逐步激活稳定性奖励 (`orientation`, `base_height`)
3. **后期训练**: 激活精细控制奖励 (`dof_vel`, `feet_stumble`)

### 监控指标
- 各奖励项的平均值和方差
- 终止率和原因分析
- 地形通过率
- 动作平滑度

## 扩展性

该奖励体系为 G1 提供了完整的行为控制框架，可以根据具体需求：
1. 调整权重平衡
2. 添加新的奖励项
3. 实现课程学习策略
4. 适配不同的任务场景

所有实现都遵循了 Isaac Gym 的标准模式，确保与现有训练流程的兼容性。
