# 成员 B（卓俊豪）实施计划：地形感知、观测空间扩展与策略适应性

## 1. 背景与角色定位
- 角色：地形穿越与观测空间设计负责人；风格评分评估协作。
- 总体目标：让 G1/H1 在多类复杂地形（坡面、楼梯、离散石、缺口等）上稳定行走，并在需要时（A 牵头）具备更“拟人”的上半身风格。
- 与评分项映射：
  - 任务评分（60%）中的跟踪/存活由稳定的感知与观测支撑。
  - 地形评分（30%）直接依赖地形观测与可泛化策略。
  - 风格评分（10%）由 A 牵头的风格奖励驱动，我负责评估与验证。

## 2. 职责清单
- 地形感知与观测空间设计：实现“前向高度扫描→特征提取→标准化→拼接入 obs_buf”。
- 适应性调试：结合地形特征在奖励与策略上做调参，加速跨越复杂地形。
- 风格评分评估：与 A 协作评估上半身对称性/自然度等指标（实现由 A 牵头）。
- 模块集成：以“地形补丁”的形式向 A/C 提供易集成的接口，优先在 G1 验证，再迁移到 H1。

## 3. 需要完成的技术任务
1) 地形感知模块
- 利用现有高度采样（_get_heights）在 base/legged_robot.py 中计算：
  - 局部坡度向量与幅值（terrain_slope_dir, terrain_slope_mag）
  - 粗糙度/起伏度（terrain_roughness，如局部高度方差/极差）
  - 阶沿/缺口线索（可选，基于高度差分/阈值检测）
- 输出统一缓存 self.terrain_features 字典，频率与 measured_heights 同步更新。

2) 观测空间扩展
- 在 compute_observations 中将上述特征（经标准化/裁剪）拼入 obs_buf。
- 校准 num_observations 并在 cfg.obs_scales 增加相应 scale 项（如 height_measurements、slope、roughness 等）。
- 上半身关节状态：目前 dof_pos/dof_vel 已包含全 23 自由度；需要核验索引与归一化范围是否合理。

3) 奖励与 scale 集成（示例见第 7 节）
- 在 cfg.rewards.scales 新增与地形特征相关的奖励权重（正/负），并在环境类中实现对应 _reward_xxx 方法。
- 与 A/C 协作调整风格/任务性奖励与地形奖励的权重平衡。

4) H1 迁移与一致性
- 复用 G1 的“地形补丁”到 H1：仅在 H1 的 compute_observations/奖励函数中做少量适配。

## 4. 观测/维度的“测量-更新-使用”闭环
- 测量（Measure）：
  - 基于 cfg.terrain.measured_points_x/y 在 base/legged_robot.py::_init_height_points 设定网格；
  - 通过 _get_heights 在 reset 与每个仿真步的 _post_physics_step_callback 中得到 measured_heights。
- 更新（Update）：
  - 在 _post_physics_step_callback 中，紧随 measured_heights 计算自定义地形特征：
    - slope_dir/mag（基于局部平面拟合或 Sobel/差分近似梯度）
    - roughness（局部标准差/极差）
  - 将计算结果缓存到 self.terrain_features（Tensor），供观测/奖励复用。
- 使用（Use）：
  - 观测：在 compute_observations 末尾拼接到 obs_buf，并按 obs_scales.xxx 归一化；
  - 奖励：在环境类（如 g1.py）实现 _reward_terrain_xxx，使用 cfg.rewards.scales.xxx 赋权叠加；
  - 可选：作为特权观测（privileged obs）给 critic 使用（若采用非对称训练）。

## 5. 与其他成员的协作点
- 与 A（风格与高速）：共同调优地形特征对奖励/策略的影响；风格评估指标由我评测、A 主导实现。
- 与 C（H1 适配）：我提供统一的 self.terrain_features 接口与最小变更指南，C 负责在 H1 环境接入与权重微调。
- 协作机制：
  - 分支策略：各自分支试验，稳定后以 feat: 前缀提交供评审与合并。
  - 参数对齐：在 hackmd 共享试验参数（特征开关、scale、权重）。

## 6. 实施方案与插入点（路径/函数级）
- 配置层（final_project/legged_gym/legged_gym/envs/g1/g1_config.py）
  - env.num_observations：扩展后需与 obs_buf 维度一致（例如 +1 增加 slope_mag → 269）。
  - terrain：确认 measured_points_x/y 合理覆盖前向区域（默认在 base/legged_robot_config 中定义）。
  - obs_scales：补充 slope、roughness 等自定义 scale（例如 slope=1.0、roughness=1.0）。
  - rewards.scales：新增 terrain_slope_penalty、terrain_alignment_reward 等。

- 基类（final_project/legged_gym/legged_gym/envs/base/legged_robot.py）
  - _post_physics_step_callback：在 self.measured_heights 就绪后调用 _compute_terrain_features（新增）。
  - 新增函数 _compute_terrain_features：
    - 从 self.measured_heights（形状：num_envs × P）恢复为网格，拟合局部平面得到坡度向量与幅值；
    - 计算 roughness（方差/极差）；
    - 归一化与裁剪（例如 tanh 或 clip 到 [-1, 1]），保存到 self.terrain_features。
  - compute_observations：从 self.terrain_features 取用所需子维度拼接入 obs_buf。

- 任务环境（final_project/legged_gym/legged_gym/envs/g1/g1.py 与 h1/h1.py）
  - 新增奖励函数（如 _reward_terrain_slope_penalty / _reward_base_align_to_slope），使用缓存的特征。
  - 与 cfg.rewards.scales 对齐，纳入 compute_reward 的加权求和流程（遵循当前工程约定）。

- 维度/接口约定
  - self.terrain_features 字典：
    - keys："slope_mag"(Tensor[N,1])、"slope_dir"(Tensor[N,2])、"roughness"(Tensor[N,1]) ...
    - 标准化：各项独立至约 [-1, 1]；必要时通过 obs_scales 再线性缩放。

## 7. 具体观测维度示例：局部坡度幅值（terrain_slope_mag）
- 设计目标：
  - 作为对地形难度与步态稳定性的重要提示；
  - 可在奖励中引入“对坡面对齐”的约束或“在大坡度下抑制不稳定动作”的惩罚。

- 计算方法（每步与 measured_heights 同步）：
  1) 将 measured_heights 恢复为 (Nx, Ny) 网格。
  2) 以局部平面 z = ax + by + c 拟合，得到法向量 n ∝ (-a, -b, 1)。
  3) 坡度幅值 slope_mag = sqrt(a^2 + b^2)，可裁剪到 [0, s_max] 后归一化到 [0,1]，再线性映射到 [-1,1]。

- 观测拼接：
  - 在 compute_observations 中拼接 slope_mag_norm（1 维），对应 obs_scales.slope（默认 1.0）。
  - 注意更新 env.num_observations。

- 奖励使用（两种常见方式，二选一或同时使用）：
  1) 惩罚型（简单直观）：
     - 名称：terrain_slope_penalty；形式：r = tanh(slope_mag_norm)^2 或 r = slope_mag_norm^2。
     - 意义：在极大坡度处抑制过激动作（结合其它奖励避免“只找平地”）。
  2) 对齐型（更物理合理）：
     - 名称：base_align_to_slope；
     - 计算：将坡度投影到机器人朝向，得到期望基座倾角 pitch* ≈ atan(slope_along_heading)，奖励基座姿态与 pitch* 的一致性：r = exp(-|pitch - pitch*| / σ)。

- 配置示例（权重需网格搜索）：
  - rewards.scales.terrain_slope_penalty = -0.05
  - rewards.scales.base_align_to_slope = 0.2

- 验证要点：
  - 统计 slope_mag 分布直方图，检查归一化是否合适；
  - 以 ablation 对比“无坡度特征/只观测/观测+奖励”的效果；
  - 关注在 stairs/gaps 上的泛化与稳定性。

## 8. 优先级与时间安排（示例）
- D1-D2：
  - 梳理现有 obs_buf 组成与 num_observations 对齐；
  - 选定 measured_points_x/y 网格与前向覆盖范围；
  - 起草 _compute_terrain_features 接口。
- D3-D4：
  - 实现 slope/roughness 计算、obs 拼接与 cfg.obs_scales；
  - 为 G1 加入 terrain_slope_penalty 或 base_align_to_slope 奖励；
  - 快速训练小步数 smoke test，验证收敛信号。
- D5-D6：
  - 调参与 ablation；
  - 将“地形补丁”对接到 H1；
  - 与 A 调整风格/任务奖励的配比（高速/稳定权衡）。
- D7+：
  - 针对失败地形（高难度台阶、缺口）专项优化，必要时引入特定几何特征（台阶边缘、缺口距离）。

## 9. 可能技术难点与对策
- 特征噪声与不稳定：
  - 使用平面拟合而非简单差分；引入滑动平均或 EMA 平滑；
  - 归一化后再裁剪；对奖励采用饱和函数（tanh）。
- 权重冲突（“只找平地”）：
  - 不要单独大权重惩罚坡度；与任务性奖励/对齐奖励搭配；
  - 通过 curriculum 与 terrain_proportions（g1_config.py）控制难度分布。
- 维度失配/报错：
  - 每次改动更新 env.num_observations；
  - 单元测试 compute_observations 输出形状与 NaN 检测。

## 10. 与项目整体目标的对应关系
- 地形特征 → 更稳的观测输入与更明确的奖励信号 → 更好的任务评分与地形评分；
- 上半身风格评估（与 A 协作）→ 满足全身仿人行走的自然度要求；
- G1→H1 的统一接口 → 降低迁移成本，保证一致性与可维护性。

## 11. 附：最小改动路线（便于落地）
- 第一步只做一个维度：slope_mag
  - cfg：
    - env.num_observations += 1
    - obs_scales.slope = 1.0
    - rewards.scales.terrain_slope_penalty = -0.05（或 base_align_to_slope = 0.2）
  - base/legged_robot.py：新增 _compute_terrain_features（仅计算 slope_mag_norm）并在 _post_physics_step_callback 中调用；compute_observations 末尾拼接。
  - g1.py：实现一个对应奖励函数；
  - 验证：打印/记录 slope_mag 范围，快速训练对比。

—— 以上计划在不打破现有结构的基础上，最小化改动面，确保可集成、可验证与可迁移。

